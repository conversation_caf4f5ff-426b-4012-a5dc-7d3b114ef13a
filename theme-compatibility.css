/**
 * Theme Compatibility CSS for Safeoid TOC
 * Add this to your theme's style.css or use it as a reference
 * to customize the TOC positioning for your specific theme
 */

/* === THEME-SPECIFIC ADJUSTMENTS === */

/* For themes with sticky headers - adjust TOC top position */
.has-sticky-header .floating-toc {
    top: 120px !important; /* Adjust based on your header height */
}

/* For themes with admin bars - additional spacing */
.admin-bar .floating-toc {
    top: calc(100px + 32px) !important; /* 32px is WP admin bar height */
}

/* For specific popular themes - uncomment and adjust as needed */

/* Astra Theme */
/*
.ast-header-sticked .floating-toc {
    top: 120px !important;
}
*/

/* GeneratePress Theme */
/*
.main-navigation.toggled .floating-toc {
    top: 150px !important;
}
*/

/* OceanWP Theme */
/*
.has-sticky-header .floating-toc {
    top: 130px !important;
}
*/

/* Elementor Header */
/*
.elementor-location-header .floating-toc {
    top: 120px !important;
}
*/

/* === CONTENT AREA ADJUSTMENTS === */

/* If your theme uses different content selectors */
.has-floating-toc .container,
.has-floating-toc .wrap,
.has-floating-toc .site-content,
.has-floating-toc .main-content {
    margin-left: 290px;
}

/* Mobile adjustments */
@media (max-width: 1024px) {
    .has-floating-toc .container,
    .has-floating-toc .wrap,
    .has-floating-toc .site-content,
    .has-floating-toc .main-content {
        margin-left: 0;
        margin-top: 60px;
    }
}

/* === CUSTOM POSITIONING === */

/* If you want to adjust the TOC position manually */
.floating-toc.custom-position {
    top: 150px !important; /* Adjust this value */
    left: 30px !important; /* Adjust this value */
}

/* If you want to change the width */
.floating-toc.custom-width {
    width: 280px !important; /* Adjust this value */
}

/* === Z-INDEX FIXES === */

/* Ensure your header stays above TOC */
.your-header-class {
    z-index: 10001 !important;
}

/* Ensure modals and popups stay above TOC */
.modal,
.popup,
.overlay {
    z-index: 10002 !important;
}

/* === HIDE TOC ON SPECIFIC PAGES === */

/* Hide TOC on homepage */
.home .floating-toc {
    display: none !important;
}

/* Hide TOC on specific page templates */
.page-template-landing .floating-toc {
    display: none !important;
}

/* === CUSTOM STYLING === */

/* Custom colors - override plugin settings */
.floating-toc.custom-colors {
    background: #f8f9fa !important;
    border-color: #e9ecef !important;
}

.floating-toc.custom-colors a {
    color: #007cba !important;
}

.floating-toc.custom-colors a:hover {
    color: #005a87 !important;
}
