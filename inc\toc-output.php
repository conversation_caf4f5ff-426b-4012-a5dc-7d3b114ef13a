<?php
/**
 * TOC Output Class
 * Generates and renders the Table of Contents HTML
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SafeoidTOCOutput {
    
    private $settings;
    private $headings = array();
    
    public function __construct() {
        $this->settings = get_option('safeoid_toc_settings', array());
        $this->set_defaults();
    }
    
    /**
     * Set default settings
     */
    private function set_defaults() {
        $defaults = array(
            'headings' => array('h2', 'h3', 'h4'),
            'toc_label' => __('Table of Contents', SAFEOID_TOC_TEXT_DOMAIN),
            'enable_desktop' => true,
            'enable_mobile' => true,
            'text_color' => '#333333',
            'link_color' => '#0073aa',
            'background_color' => '#ffffff',
            'disable_schema' => false
        );
        
        $this->settings = wp_parse_args($this->settings, $defaults);
    }
    
    /**
     * Render the TOC
     */
    public function render() {
        if (!$this->should_display_toc()) {
            return;
        }
        
        $this->extract_headings();
        
        if (empty($this->headings)) {
            return;
        }
        
        $this->output_css_variables();
        $this->output_desktop_toc();
        $this->output_mobile_toc();
    }
    
    /**
     * Check if TOC should be displayed
     */
    private function should_display_toc() {
        // Don't show on admin pages
        if (is_admin()) {
            return false;
        }
        
        // Only show on posts and pages by default
        if (!is_single() && !is_page()) {
            return false;
        }
        
        // Check if at least one display option is enabled
        if (!$this->settings['enable_desktop'] && !$this->settings['enable_mobile']) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Extract headings from current post content
     */
    private function extract_headings() {
        global $post;
        
        if (!$post || empty($post->post_content)) {
            return;
        }
        
        $content = apply_filters('the_content', $post->post_content);
        $enabled_headings = $this->settings['headings'];
        
        if (empty($enabled_headings)) {
            return;
        }
        
        $heading_pattern = '<(' . implode('|', $enabled_headings) . ')([^>]*?)id=["\']([^"\']*)["\']([^>]*?)>(.*?)</\\1>';
        
        preg_match_all('/' . $heading_pattern . '/i', $content, $matches, PREG_SET_ORDER);
        
        foreach ($matches as $match) {
            $level = strtolower($match[1]);
            $id = $match[3];
            $text = wp_strip_all_tags($match[5]);
            
            if (!empty($id) && !empty($text)) {
                $this->headings[] = array(
                    'level' => $level,
                    'id' => $id,
                    'text' => $text,
                    'depth' => (int) substr($level, 1)
                );
            }
        }
    }
    
    /**
     * Output CSS custom properties for colors
     */
    private function output_css_variables() {
        echo '<style>';
        echo ':root {';
        echo '--safeoid-toc-text-color: ' . esc_attr($this->settings['text_color']) . ';';
        echo '--safeoid-toc-link-color: ' . esc_attr($this->settings['link_color']) . ';';
        echo '--safeoid-toc-bg-color: ' . esc_attr($this->settings['background_color']) . ';';
        echo '--safeoid-toc-link-hover-color: ' . esc_attr($this->adjust_color_brightness($this->settings['link_color'], 20)) . ';';
        echo '}';
        echo '</style>';
    }
    
    /**
     * Output desktop TOC
     */
    private function output_desktop_toc() {
        if (!$this->settings['enable_desktop']) {
            return;
        }
        
        echo '<div class="safeoid-toc desktop">';
        echo '<h3 class="toc-heading">' . esc_html($this->settings['toc_label']) . '</h3>';
        echo '<div class="toc-content">';
        $this->output_toc_list();
        echo '</div>';
        echo '</div>';
    }
    
    /**
     * Output mobile TOC
     */
    private function output_mobile_toc() {
        if (!$this->settings['enable_mobile']) {
            return;
        }
        
        echo '<div class="safeoid-toc mobile">';
        echo '<div class="toc-toggle">' . esc_html($this->settings['toc_label']) . '</div>';
        echo '<div class="toc-content">';
        $this->output_toc_list();
        echo '</div>';
        echo '</div>';
    }
    
    /**
     * Output the TOC list structure
     */
    private function output_toc_list() {
        if (empty($this->headings)) {
            return;
        }
        
        $current_depth = 0;
        $list_stack = array();
        
        echo '<ul>';
        $current_depth = $this->headings[0]['depth'];
        
        foreach ($this->headings as $heading) {
            $depth = $heading['depth'];
            
            // Handle depth changes
            if ($depth > $current_depth) {
                // Going deeper - open new nested list
                for ($i = $current_depth; $i < $depth; $i++) {
                    echo '<ul>';
                    array_push($list_stack, $i);
                }
            } elseif ($depth < $current_depth) {
                // Going shallower - close nested lists
                for ($i = $current_depth; $i > $depth; $i--) {
                    echo '</ul>';
                    array_pop($list_stack);
                }
            }
            
            // Output the list item
            echo '<li>';
            echo '<a href="#' . esc_attr($heading['id']) . '">';
            echo esc_html($heading['text']);
            echo '</a>';
            echo '</li>';
            
            $current_depth = $depth;
        }
        
        // Close any remaining open lists
        while (!empty($list_stack)) {
            echo '</ul>';
            array_pop($list_stack);
        }
        
        echo '</ul>';
    }
    
    /**
     * Adjust color brightness
     */
    private function adjust_color_brightness($hex, $percent) {
        // Remove # if present
        $hex = ltrim($hex, '#');
        
        // Convert to RGB
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        // Adjust brightness
        $r = max(0, min(255, $r + ($percent * 255 / 100)));
        $g = max(0, min(255, $g + ($percent * 255 / 100)));
        $b = max(0, min(255, $b + ($percent * 255 / 100)));
        
        // Convert back to hex
        return '#' . sprintf('%02x%02x%02x', $r, $g, $b);
    }
    
    /**
     * Get headings for external use (e.g., schema, Elementor widget)
     */
    public function get_headings() {
        if (empty($this->headings)) {
            $this->extract_headings();
        }
        return $this->headings;
    }
}
