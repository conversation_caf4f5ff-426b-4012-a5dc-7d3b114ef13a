<?php
/**
 * Elementor TOC Widget
 * Custom Elementor widget for displaying the Table of Contents
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Safeoid TOC Elementor Widget
 */
class SafeoidTOCElementorWidget extends \Elementor\Widget_Base {
    
    /**
     * Get widget name
     */
    public function get_name() {
        return 'safeoid_toc';
    }
    
    /**
     * Get widget title
     */
    public function get_title() {
        return __('Floating TOC', SAFEOID_TOC_TEXT_DOMAIN);
    }
    
    /**
     * Get widget icon
     */
    public function get_icon() {
        return 'eicon-table-of-contents';
    }
    
    /**
     * Get widget categories
     */
    public function get_categories() {
        return ['general'];
    }
    
    /**
     * Get widget keywords
     */
    public function get_keywords() {
        return ['toc', 'table of contents', 'navigation', 'safeoid'];
    }
    
    /**
     * Register widget controls
     */
    protected function _register_controls() {
        $this->start_controls_section(
            'section_content',
            [
                'label' => __('TOC Settings', SAFEOID_TOC_TEXT_DOMAIN),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );
        
        $this->add_control(
            'toc_title',
            [
                'label' => __('TOC Title', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Table of Contents', SAFEOID_TOC_TEXT_DOMAIN),
                'placeholder' => __('Enter your title', SAFEOID_TOC_TEXT_DOMAIN),
            ]
        );
        
        $this->add_control(
            'headings',
            [
                'label' => __('Include Headings', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::SELECT2,
                'multiple' => true,
                'options' => [
                    'h1' => 'H1',
                    'h2' => 'H2',
                    'h3' => 'H3',
                    'h4' => 'H4',
                    'h5' => 'H5',
                    'h6' => 'H6',
                ],
                'default' => ['h2', 'h3', 'h4'],
            ]
        );
        
        $this->add_control(
            'use_global_settings',
            [
                'label' => __('Use Global Settings', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', SAFEOID_TOC_TEXT_DOMAIN),
                'label_off' => __('No', SAFEOID_TOC_TEXT_DOMAIN),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );
        
        $this->end_controls_section();
        
        $this->start_controls_section(
            'section_style',
            [
                'label' => __('TOC Style', SAFEOID_TOC_TEXT_DOMAIN),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => [
                    'use_global_settings' => '',
                ],
            ]
        );
        
        $this->add_control(
            'text_color',
            [
                'label' => __('Text Color', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#333333',
                'selectors' => [
                    '{{WRAPPER}} .safeoid-toc' => 'color: {{VALUE}};',
                ],
            ]
        );
        
        $this->add_control(
            'link_color',
            [
                'label' => __('Link Color', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#0073aa',
                'selectors' => [
                    '{{WRAPPER}} .safeoid-toc a' => 'color: {{VALUE}};',
                ],
            ]
        );
        
        $this->add_control(
            'background_color',
            [
                'label' => __('Background Color', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .safeoid-toc' => 'background-color: {{VALUE}};',
                ],
            ]
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'border',
                'selector' => '{{WRAPPER}} .safeoid-toc',
                'separator' => 'before',
            ]
        );
        
        $this->add_control(
            'border_radius',
            [
                'label' => __('Border Radius', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .safeoid-toc' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'box_shadow',
                'selector' => '{{WRAPPER}} .safeoid-toc',
            ]
        );
        
        $this->add_responsive_control(
            'padding',
            [
                'label' => __('Padding', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .safeoid-toc' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '20',
                    'right' => '20',
                    'bottom' => '20',
                    'left' => '20',
                    'unit' => 'px',
                    'isLinked' => true,
                ],
            ]
        );
        
        $this->end_controls_section();
    }
    
    /**
     * Render widget output
     */
    protected function render() {
        $settings = $this->get_settings_for_display();
        
        // Get global settings if enabled
        $global_settings = get_option('safeoid_toc_settings', array());
        $use_global = $settings['use_global_settings'] === 'yes';
        
        // Determine which settings to use
        $toc_title = $use_global ? $global_settings['toc_label'] : $settings['toc_title'];
        $headings = $use_global ? $global_settings['headings'] : $settings['headings'];
        
        // Include TOC output class
        include_once SAFEOID_TOC_PLUGIN_DIR . 'inc/toc-output.php';
        $toc_output = new SafeoidTOCOutput();
        $headings_data = $toc_output->get_headings();
        
        if (empty($headings_data)) {
            echo '<div class="floating-toc elementor-widget">';
            echo '<h3 class="toc-heading">' . esc_html($toc_title) . '</h3>';
            echo '<div class="toc-content">';
            echo '<p>' . __('No headings found in the content.', SAFEOID_TOC_TEXT_DOMAIN) . '</p>';
            echo '</div>';
            echo '</div>';
            return;
        }

        // Output custom CSS if not using global settings
        if (!$use_global) {
            echo '<style>';
            echo '.elementor-element-' . $this->get_id() . ' .floating-toc {';
            echo '--safeoid-toc-text-color: ' . esc_attr($settings['text_color']) . ';';
            echo '--safeoid-toc-link-color: ' . esc_attr($settings['link_color']) . ';';
            echo '--safeoid-toc-bg-color: ' . esc_attr($settings['background_color']) . ';';
            echo '}';
            echo '</style>';
        }

        // Output TOC
        echo '<div class="floating-toc elementor-widget">';
        echo '<h3 class="toc-heading">' . esc_html($toc_title) . '</h3>';
        echo '<div class="toc-content">';
        
        // Output TOC list
        if (!empty($headings_data)) {
            $current_depth = 0;
            $list_stack = array();

            echo '<ul>';
            $current_depth = $headings_data[0]['depth'];

            foreach ($headings_data as $heading) {
                $depth = $heading['depth'];

                // Handle depth changes
                if ($depth > $current_depth) {
                    // Going deeper - open new nested list
                    for ($i = $current_depth; $i < $depth; $i++) {
                        echo '<ul>';
                        array_push($list_stack, $i);
                    }
                } elseif ($depth < $current_depth) {
                    // Going shallower - close nested lists
                    for ($i = $current_depth; $i > $depth; $i--) {
                        echo '</ul>';
                        array_pop($list_stack);
                    }
                }

                // Output the list item
                echo '<li>';
                echo '<a href="#' . esc_attr($heading['id']) . '">';
                echo esc_html($heading['text']);
                echo '</a>';
                echo '</li>';

                $current_depth = $depth;
            }

            // Close any remaining open lists
            while (!empty($list_stack)) {
                echo '</ul>';
                array_pop($list_stack);
            }

            echo '</ul>';
        } else {
            // Show debug info for admins
            if (current_user_can('manage_options')) {
                echo '<p>' . __('No headings found in the content.', SAFEOID_TOC_TEXT_DOMAIN) . '</p>';
                echo '<p><small>' . __('(Add ?toc_debug=1 to the URL to see debug information)', SAFEOID_TOC_TEXT_DOMAIN) . '</small></p>';
            } else {
                echo '<p>' . __('No headings found in the content.', SAFEOID_TOC_TEXT_DOMAIN) . '</p>';
            }
        }
        
        echo '</div>';
        echo '</div>';
    }
}
