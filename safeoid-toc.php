<?php
/**
 * Plugin Name: Side Responsive TOC by Safeoid
 * Plugin URI: https://safeoid.com/floating-toc
 * Description: A lightweight, responsive Table of Contents plugin with floating sidebar on desktop and sticky header on mobile. Includes Elementor integration and SEO schema markup.
 * Version: 1.0.0
 * Author: Safeoid
 * License: GPLv2 or later
 * Text Domain: safeoid-toc
 * Requires at least: 6.5
 * Tested up to: 6.5
 * Requires PHP: 7.4
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SAFEOID_TOC_VERSION', '1.0.0');
define('SAFEOID_TOC_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('SAFEOID_TOC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SAFEOID_TOC_TEXT_DOMAIN', 'safeoid-toc');

/**
 * Main plugin class
 */
class SafeoidTOC {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_footer', array($this, 'output_toc'));
        add_action('wp_head', array($this, 'output_schema'));
        
        // Elementor integration
        add_action('elementor/widgets/widgets_registered', array($this, 'register_elementor_widget'));
        
        // Add filter to modify content and add IDs to headings
        add_filter('the_content', array($this, 'add_heading_ids'), 10);
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        load_plugin_textdomain(SAFEOID_TOC_TEXT_DOMAIN, false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        if (!is_admin()) {
            wp_enqueue_style(
                'safeoid-toc-style',
                SAFEOID_TOC_PLUGIN_URL . 'css/style.css',
                array(),
                SAFEOID_TOC_VERSION
            );
            
            wp_enqueue_script(
                'safeoid-toc-script',
                SAFEOID_TOC_PLUGIN_URL . 'js/toc.js',
                array(),
                SAFEOID_TOC_VERSION,
                true
            );
        }
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Safeoid TOC Settings', SAFEOID_TOC_TEXT_DOMAIN),
            __('Safeoid TOC', SAFEOID_TOC_TEXT_DOMAIN),
            'manage_options',
            'safeoid-toc-settings',
            array($this, 'settings_page'),
            'dashicons-list-view',
            30
        );
    }
    
    /**
     * Settings page callback
     */
    public function settings_page() {
        include_once SAFEOID_TOC_PLUGIN_DIR . 'inc/settings-page.php';
    }
    
    /**
     * Output TOC in footer
     */
    public function output_toc() {
        if (!is_admin() && (is_single() || is_page())) {
            include_once SAFEOID_TOC_PLUGIN_DIR . 'inc/toc-output.php';
            $toc_output = new SafeoidTOCOutput();
            $toc_output->render();
        }
    }
    
    /**
     * Output schema markup
     */
    public function output_schema() {
        if (!is_admin() && (is_single() || is_page())) {
            $settings = get_option('safeoid_toc_settings', array());
            if (!isset($settings['disable_schema']) || !$settings['disable_schema']) {
                include_once SAFEOID_TOC_PLUGIN_DIR . 'inc/schema.php';
                $schema = new SafeoidTOCSchema();
                $schema->output();
            }
        }
    }
    
    /**
     * Register Elementor widget
     */
    public function register_elementor_widget() {
        if (class_exists('Elementor\Widget_Base')) {
            include_once SAFEOID_TOC_PLUGIN_DIR . 'widgets/elementor-toc-widget.php';
            \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new SafeoidTOCElementorWidget());
        }
    }
    
    /**
     * Add IDs to headings in content
     */
    public function add_heading_ids($content) {
        if (!is_single() && !is_page()) {
            return $content;
        }

        $settings = get_option('safeoid_toc_settings', array());
        $enabled_headings = isset($settings['headings']) ? $settings['headings'] : array('h2', 'h3', 'h4');

        if (empty($enabled_headings)) {
            return $content;
        }

        // Pattern to match headings without IDs
        $heading_pattern = '<(' . implode('|', $enabled_headings) . ')([^>]*?)>(.*?)</\\1>';

        return preg_replace_callback(
            '/' . $heading_pattern . '/i',
            array($this, 'add_id_to_heading'),
            $content
        );
    }

    /**
     * Callback to add ID to heading
     */
    private function add_id_to_heading($matches) {
        $tag = $matches[1];
        $attributes = $matches[2];
        $content = $matches[3];

        // Check if ID already exists
        if (strpos($attributes, 'id=') !== false) {
            return $matches[0];
        }

        // Generate ID from content
        $id = $this->generate_heading_id($content);

        // Add the ID attribute to the heading
        return '<' . $tag . $attributes . ' id="' . esc_attr($id) . '">' . $content . '</' . $tag . '>';
    }

    /**
     * Generate heading ID from content
     */
    private function generate_heading_id($content) {
        // Remove HTML tags
        $text = wp_strip_all_tags($content);

        // Convert to lowercase and replace spaces with hyphens
        $id = strtolower(trim($text));
        $id = preg_replace('/[^a-z0-9\-_]/', '', str_replace(' ', '-', $id));

        // Remove multiple consecutive hyphens
        $id = preg_replace('/-+/', '-', $id);

        // Remove leading/trailing hyphens
        $id = trim($id, '-');

        // Ensure ID is not empty
        if (empty($id)) {
            $id = 'heading-' . uniqid();
        }

        return $id;
    }
}

// Initialize the plugin
new SafeoidTOC();

/**
 * Activation hook
 */
register_activation_hook(__FILE__, 'safeoid_toc_activate');
function safeoid_toc_activate() {
    // Set default options
    $default_settings = array(
        'headings' => array('h2', 'h3', 'h4'),
        'toc_label' => __('Table of Contents', SAFEOID_TOC_TEXT_DOMAIN),
        'enable_desktop' => true,
        'enable_mobile' => true,
        'text_color' => '#333333',
        'link_color' => '#0073aa',
        'background_color' => '#ffffff',
        'disable_schema' => false
    );

    add_option('safeoid_toc_settings', $default_settings);
}

/**
 * Deactivation hook
 */
register_deactivation_hook(__FILE__, 'safeoid_toc_deactivate');
function safeoid_toc_deactivate() {
    // Clean up hooks - WordPress handles this automatically
}
