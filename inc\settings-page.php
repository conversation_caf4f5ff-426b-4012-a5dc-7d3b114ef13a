<?php
/**
 * Settings Page
 * Admin interface for configuring the TOC plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle form submission
if (isset($_POST['submit']) && wp_verify_nonce($_POST['safeoid_toc_nonce'], 'safeoid_toc_settings')) {
    $settings = array(
        'headings' => isset($_POST['headings']) ? array_map('sanitize_text_field', $_POST['headings']) : array(),
        'toc_label' => sanitize_text_field($_POST['toc_label']),
        'enable_desktop' => isset($_POST['enable_desktop']),
        'enable_mobile' => isset($_POST['enable_mobile']),
        'text_color' => sanitize_hex_color($_POST['text_color']),
        'link_color' => sanitize_hex_color($_POST['link_color']),
        'background_color' => sanitize_hex_color($_POST['background_color']),
        'disable_schema' => isset($_POST['disable_schema'])
    );
    
    update_option('safeoid_toc_settings', $settings);
    echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', SAFEOID_TOC_TEXT_DOMAIN) . '</p></div>';
}

// Get current settings
$settings = get_option('safeoid_toc_settings', array());
$defaults = array(
    'headings' => array('h2', 'h3', 'h4'),
    'toc_label' => __('Table of Contents', SAFEOID_TOC_TEXT_DOMAIN),
    'enable_desktop' => true,
    'enable_mobile' => true,
    'text_color' => '#333333',
    'link_color' => '#0073aa',
    'background_color' => '#ffffff',
    'disable_schema' => false
);
$settings = wp_parse_args($settings, $defaults);
?>

<div class="wrap">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
    
    <form method="post" action="">
        <?php wp_nonce_field('safeoid_toc_settings', 'safeoid_toc_nonce'); ?>
        
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label><?php _e('Include Headings', SAFEOID_TOC_TEXT_DOMAIN); ?></label>
                </th>
                <td>
                    <fieldset>
                        <legend class="screen-reader-text"><?php _e('Select which headings to include in TOC', SAFEOID_TOC_TEXT_DOMAIN); ?></legend>
                        <?php
                        $heading_options = array(
                            'h1' => 'H1',
                            'h2' => 'H2',
                            'h3' => 'H3',
                            'h4' => 'H4',
                            'h5' => 'H5',
                            'h6' => 'H6'
                        );
                        
                        foreach ($heading_options as $value => $label) {
                            $checked = in_array($value, $settings['headings']) ? 'checked="checked"' : '';
                            echo '<label><input type="checkbox" name="headings[]" value="' . esc_attr($value) . '" ' . $checked . '> ' . esc_html($label) . '</label><br>';
                        }
                        ?>
                        <p class="description"><?php _e('Select which heading levels to include in the Table of Contents.', SAFEOID_TOC_TEXT_DOMAIN); ?></p>
                    </fieldset>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="toc_label"><?php _e('TOC Label', SAFEOID_TOC_TEXT_DOMAIN); ?></label>
                </th>
                <td>
                    <input type="text" id="toc_label" name="toc_label" value="<?php echo esc_attr($settings['toc_label']); ?>" class="regular-text">
                    <p class="description"><?php _e('The heading text displayed above the Table of Contents.', SAFEOID_TOC_TEXT_DOMAIN); ?></p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label><?php _e('Display Options', SAFEOID_TOC_TEXT_DOMAIN); ?></label>
                </th>
                <td>
                    <fieldset>
                        <legend class="screen-reader-text"><?php _e('Choose where to display the TOC', SAFEOID_TOC_TEXT_DOMAIN); ?></legend>
                        <label>
                            <input type="checkbox" name="enable_desktop" value="1" <?php checked($settings['enable_desktop']); ?>>
                            <?php _e('Enable floating TOC on desktop', SAFEOID_TOC_TEXT_DOMAIN); ?>
                        </label><br>
                        <label>
                            <input type="checkbox" name="enable_mobile" value="1" <?php checked($settings['enable_mobile']); ?>>
                            <?php _e('Enable sticky TOC on mobile', SAFEOID_TOC_TEXT_DOMAIN); ?>
                        </label>
                        <p class="description"><?php _e('Choose where the TOC should be displayed. Desktop shows a floating sidebar, mobile shows a collapsible header.', SAFEOID_TOC_TEXT_DOMAIN); ?></p>
                    </fieldset>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="text_color"><?php _e('Text Color', SAFEOID_TOC_TEXT_DOMAIN); ?></label>
                </th>
                <td>
                    <input type="color" id="text_color" name="text_color" value="<?php echo esc_attr($settings['text_color']); ?>">
                    <p class="description"><?php _e('Color for the TOC text and headings.', SAFEOID_TOC_TEXT_DOMAIN); ?></p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="link_color"><?php _e('Link Color', SAFEOID_TOC_TEXT_DOMAIN); ?></label>
                </th>
                <td>
                    <input type="color" id="link_color" name="link_color" value="<?php echo esc_attr($settings['link_color']); ?>">
                    <p class="description"><?php _e('Color for the TOC links.', SAFEOID_TOC_TEXT_DOMAIN); ?></p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="background_color"><?php _e('Background Color', SAFEOID_TOC_TEXT_DOMAIN); ?></label>
                </th>
                <td>
                    <input type="color" id="background_color" name="background_color" value="<?php echo esc_attr($settings['background_color']); ?>">
                    <p class="description"><?php _e('Background color for the TOC container.', SAFEOID_TOC_TEXT_DOMAIN); ?></p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label><?php _e('SEO Options', SAFEOID_TOC_TEXT_DOMAIN); ?></label>
                </th>
                <td>
                    <fieldset>
                        <legend class="screen-reader-text"><?php _e('SEO and Schema options', SAFEOID_TOC_TEXT_DOMAIN); ?></legend>
                        <label>
                            <input type="checkbox" name="disable_schema" value="1" <?php checked($settings['disable_schema']); ?>>
                            <?php _e('Disable schema markup output', SAFEOID_TOC_TEXT_DOMAIN); ?>
                        </label>
                        <p class="description"><?php _e('By default, the plugin outputs TableOfContents schema markup for SEO. Check this to disable it if you have conflicts with other SEO plugins.', SAFEOID_TOC_TEXT_DOMAIN); ?></p>
                    </fieldset>
                </td>
            </tr>
        </table>
        
        <?php submit_button(); ?>
    </form>
    
    <div class="safeoid-toc-info" style="margin-top: 40px; padding: 20px; background: #f9f9f9; border-left: 4px solid #0073aa;">
        <h3><?php _e('Plugin Information', SAFEOID_TOC_TEXT_DOMAIN); ?></h3>
        <p><strong><?php _e('Version:', SAFEOID_TOC_TEXT_DOMAIN); ?></strong> <?php echo SAFEOID_TOC_VERSION; ?></p>
        <p><strong><?php _e('Author:', SAFEOID_TOC_TEXT_DOMAIN); ?></strong> <a href="https://safeoid.com" target="_blank">Safeoid</a></p>
        <p><strong><?php _e('Plugin URI:', SAFEOID_TOC_TEXT_DOMAIN); ?></strong> <a href="https://safeoid.com/floating-toc" target="_blank">https://safeoid.com/floating-toc</a></p>
        
        <h4><?php _e('Features:', SAFEOID_TOC_TEXT_DOMAIN); ?></h4>
        <ul>
            <li><?php _e('✅ Responsive design - floating sidebar on desktop, sticky header on mobile', SAFEOID_TOC_TEXT_DOMAIN); ?></li>
            <li><?php _e('✅ No jQuery dependency - pure vanilla JavaScript', SAFEOID_TOC_TEXT_DOMAIN); ?></li>
            <li><?php _e('✅ No database writes - dynamically generated', SAFEOID_TOC_TEXT_DOMAIN); ?></li>
            <li><?php _e('✅ SEO-friendly with schema markup', SAFEOID_TOC_TEXT_DOMAIN); ?></li>
            <li><?php _e('✅ Elementor widget included', SAFEOID_TOC_TEXT_DOMAIN); ?></li>
            <li><?php _e('✅ Compatible with caching plugins', SAFEOID_TOC_TEXT_DOMAIN); ?></li>
        </ul>
        
        <h4><?php _e('Usage:', SAFEOID_TOC_TEXT_DOMAIN); ?></h4>
        <p><?php _e('The TOC will automatically appear on posts and pages that contain the selected heading levels. For Elementor users, you can also drag the "Floating TOC" widget onto any page.', SAFEOID_TOC_TEXT_DOMAIN); ?></p>
    </div>
</div>

<style>
.safeoid-toc-info ul {
    list-style-type: none;
    padding-left: 0;
}

.safeoid-toc-info ul li {
    margin-bottom: 5px;
}
</style>
