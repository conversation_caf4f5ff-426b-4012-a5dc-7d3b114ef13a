/**
 * Safeoid TOC Styles
 * Responsive Table of Contents styling
 */

/* Common styles for both desktop and mobile */
.safeoid-toc {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    line-height: 1.5;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.safeoid-toc * {
    box-sizing: border-box;
}

.safeoid-toc .toc-heading {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 15px 0;
    padding: 0 0 10px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.safeoid-toc ul {
    margin: 0;
    padding: 0 0 0 20px;
    list-style-type: none;
}

.safeoid-toc ul li {
    margin: 0 0 8px 0;
    padding: 0;
    font-size: 14px;
    line-height: 1.4;
}

.safeoid-toc ul li a {
    text-decoration: none;
    transition: color 0.2s ease;
}

.safeoid-toc ul li a:hover {
    text-decoration: underline;
}

.safeoid-toc ul ul {
    margin-top: 8px;
    margin-bottom: 0;
}

/* Desktop styles - floating sidebar */
@media screen and (min-width: 768px) {
    body.safeoid-toc-active {
        margin-left: 280px;
        transition: margin-left 0.3s ease;
    }
    
    .safeoid-toc.desktop {
        position: fixed;
        top: 32px; /* WP admin bar height */
        left: 0;
        width: 260px;
        height: calc(100vh - 32px);
        background-color: #fff;
        border-right: 1px solid rgba(0, 0, 0, 0.1);
        padding: 20px;
        overflow-y: auto;
        z-index: 999;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease;
    }
    
    /* Adjust for logged-out users */
    body:not(.admin-bar) .safeoid-toc.desktop {
        top: 0;
        height: 100vh;
    }
    
    .safeoid-toc.desktop .toc-toggle {
        display: none;
    }
}

/* Mobile styles - sticky header */
@media screen and (max-width: 767px) {
    .safeoid-toc.mobile {
        position: sticky;
        top: 0;
        width: 100%;
        background-color: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        padding: 0;
        z-index: 999;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    
    .safeoid-toc.mobile .toc-heading {
        display: none;
    }
    
    .safeoid-toc.mobile .toc-toggle {
        display: block;
        padding: 12px 15px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        user-select: none;
        background-color: #f8f8f8;
    }
    
    .safeoid-toc.mobile .toc-toggle:before {
        content: "☰";
        margin-right: 8px;
    }
    
    .safeoid-toc.mobile .toc-content {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        padding: 0 15px;
    }
    
    .safeoid-toc.mobile.active .toc-content {
        max-height: 70vh;
        padding: 15px;
        overflow-y: auto;
    }
    
    .safeoid-toc.mobile.active .toc-toggle:before {
        content: "✕";
    }
}

/* Elementor widget styles */
.elementor-widget-safeoid-toc .safeoid-toc {
    position: relative;
    top: auto;
    left: auto;
    width: 100%;
    height: auto;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.elementor-widget-safeoid-toc .safeoid-toc .toc-toggle {
    display: none;
}

/* Customizable colors via settings */
.safeoid-toc {
    color: var(--safeoid-toc-text-color, #333333);
    background-color: var(--safeoid-toc-bg-color, #ffffff);
}

.safeoid-toc a {
    color: var(--safeoid-toc-link-color, #0073aa);
}

.safeoid-toc a:hover {
    color: var(--safeoid-toc-link-hover-color, #00a0d2);
}

/* Print styles */
@media print {
    .safeoid-toc {
        display: none !important;
    }
    
    body.safeoid-toc-active {
        margin-left: 0 !important;
    }
}
