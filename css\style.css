/**
 * Safeoid TOC Styles
 * Responsive Table of Contents styling
 */

/* Push main content on desktop when TOC is present */
body:has(.floating-toc) {
    padding-left: 290px;
}

/* Fallback for browsers that don't support :has() */
body.has-floating-toc {
    padding-left: 290px;
}

/* Exclude header and footer from the padding effect */
body:has(.floating-toc) header,
body:has(.floating-toc) .site-header,
body:has(.floating-toc) .main-header,
body:has(.floating-toc) .header,
body:has(.floating-toc) nav.navbar,
body:has(.floating-toc) .top-bar,
body:has(.floating-toc) footer,
body:has(.floating-toc) .site-footer,
body:has(.floating-toc) .main-footer,
body:has(.floating-toc) .footer,
body.has-floating-toc header,
body.has-floating-toc .site-header,
body.has-floating-toc .main-header,
body.has-floating-toc .header,
body.has-floating-toc nav.navbar,
body.has-floating-toc .top-bar,
body.has-floating-toc footer,
body.has-floating-toc .site-footer,
body.has-floating-toc .main-footer,
body.has-floating-toc .footer {
    margin-left: -290px;
    width: calc(100% + 290px);
    position: relative;
    z-index: 10000;
}

.floating-toc {
    position: fixed;
    top: 60px;
    left: 20px;
    width: 250px;
    background: var(--safeoid-toc-bg-color, #fff);
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    z-index: 9999;
    font-family: Arial, sans-serif;
    color: var(--safeoid-toc-text-color, #111);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.floating-toc .toc-heading {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    color: var(--safeoid-toc-text-color, #111);
}

.floating-toc .toc-toggle {
    display: none;
}

.floating-toc ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.floating-toc ul li {
    margin: 8px 0;
}

.floating-toc ul li a {
    text-decoration: none;
    color: var(--safeoid-toc-link-color, #0073e6);
    font-size: 14px;
}

.floating-toc ul li a:hover {
    text-decoration: underline;
    color: var(--safeoid-toc-link-hover-color, #005bb5);
}

@media (max-width: 1024px) {
    /* Remove desktop body padding on mobile */
    body:has(.floating-toc),
    body.has-floating-toc {
        padding-left: 0;
        margin-top: 60px; /* pushes content below TOC */
    }

    /* Reset header/footer margins on mobile */
    body:has(.floating-toc) header,
    body:has(.floating-toc) .site-header,
    body:has(.floating-toc) .main-header,
    body:has(.floating-toc) .header,
    body:has(.floating-toc) nav.navbar,
    body:has(.floating-toc) .top-bar,
    body:has(.floating-toc) footer,
    body:has(.floating-toc) .site-footer,
    body:has(.floating-toc) .main-footer,
    body:has(.floating-toc) .footer,
    body.has-floating-toc header,
    body.has-floating-toc .site-header,
    body.has-floating-toc .main-header,
    body.has-floating-toc .header,
    body.has-floating-toc nav.navbar,
    body.has-floating-toc .top-bar,
    body.has-floating-toc footer,
    body.has-floating-toc .site-footer,
    body.has-floating-toc .main-footer,
    body.has-floating-toc .footer {
        margin-left: 0;
        width: 100%;
    }

    .floating-toc {
        top: 0;
        left: 0;
        width: 100%;
        border-radius: 0;
        border: none;
        padding: 12px 16px;
    }

    .floating-toc .toc-heading {
        display: none;
    }

    .floating-toc .toc-toggle {
        display: block;
        font-weight: bold;
        font-size: 16px;
        cursor: pointer;
        color: var(--safeoid-toc-text-color, #111);
    }

    .floating-toc .toc-content {
        display: none;
        margin-top: 10px;
    }

    body.toc-open .floating-toc .toc-content {
        display: block;
    }
}

/* Customizable colors via settings */
.floating-toc {
    color: var(--safeoid-toc-text-color, #111);
    background-color: var(--safeoid-toc-bg-color, #ffffff);
}

.floating-toc a {
    color: var(--safeoid-toc-link-color, #0073e6);
}

.floating-toc a:hover {
    color: var(--safeoid-toc-link-hover-color, #005bb5);
}

/* Elementor widget styles */
.elementor-widget-safeoid-toc .floating-toc {
    position: relative;
    top: auto;
    left: auto;
    width: 100%;
    height: auto;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.elementor-widget-safeoid-toc .floating-toc .toc-toggle {
    display: none;
}



/* Print styles */
@media print {
    .floating-toc {
        display: none !important;
    }
}
