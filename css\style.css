/**
 * Safeoid TOC Styles
 * Responsive Table of Contents styling
 */

/* === Desktop Floating TOC === */
.floating-toc {
  position: fixed;
  top: 60px; /* Adjust this to match desktop header height */
  left: 20px;
  width: 220px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  z-index: 9999;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-family: Arial, sans-serif;
  color: #111;
}

.floating-toc .toc-heading {
  display: block;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #111;
}

.floating-toc .toc-toggle {
  display: none;
}

.floating-toc ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.floating-toc ul li {
  margin: 8px 0;
}

.floating-toc ul li a {
  text-decoration: none;
  color: #0073e6;
  font-size: 14px;
}

.floating-toc ul li a:hover {
  text-decoration: underline;
}

/* Push main content on desktop */
body {
  padding-left: 260px;
}

/* === Tablet/Mobile TOC: Stick to VERY TOP === */
@media (max-width: 1024px) {
  body {
    padding-left: 0;
    margin-top: 60px; /* pushes content below TOC */
  }

  .floating-toc {
    position: fixed;
    top: 0; /* stick to top */
    left: 0;
    right: 0;
    width: 100%;
    border-radius: 0;
    border: none;
    padding: 12px 16px;
    background: #ffffff;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  }

  .floating-toc .toc-heading {
    display: none; /* hide h3 */
  }

  .floating-toc .toc-toggle {
    display: block;
    font-weight: bold;
    font-size: 16px;
    cursor: pointer;
    color: #111;
  }

  .floating-toc .toc-content {
    display: none;
    margin-top: 10px;
  }

  body.toc-open .floating-toc .toc-content {
    display: block;
  }

  .floating-toc ul li a {
    font-size: 15px;
  }
}

/* Customizable colors via settings */
.floating-toc {
    color: var(--safeoid-toc-text-color, #111);
    background-color: var(--safeoid-toc-bg-color, #ffffff);
}

.floating-toc a {
    color: var(--safeoid-toc-link-color, #0073e6);
}

.floating-toc a:hover {
    color: var(--safeoid-toc-link-hover-color, #005bb5);
}

/* Elementor widget styles */
.elementor-widget-safeoid-toc .floating-toc {
    position: relative;
    top: auto;
    left: auto;
    width: 100%;
    height: auto;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.elementor-widget-safeoid-toc .floating-toc .toc-toggle {
    display: none;
}

/* Print styles */
@media print {
    .floating-toc {
        display: none !important;
    }

    body {
        padding-left: 0 !important;
        margin-top: 0 !important;
    }
}
