/**
 * Safeoid TOC Styles
 * Responsive Table of Contents styling
 */

/* Push main content on desktop when TOC is present */
body:has(.floating-toc) {
    padding-left: 290px;
}

/* Fallback for browsers that don't support :has() */
body.has-floating-toc {
    padding-left: 290px;
}

/* But don't push header and footer */
body:has(.floating-toc) header,
body:has(.floating-toc) .site-header,
body:has(.floating-toc) footer,
body:has(.floating-toc) .site-footer,
body.has-floating-toc header,
body.has-floating-toc .site-header,
body.has-floating-toc footer,
body.has-floating-toc .site-footer {
    margin-left: -290px;
    width: calc(100% + 290px);
}

.floating-toc {
    position: fixed;
    top: 100px; /* Adjust based on your header height */
    left: 20px;
    width: 250px;
    max-height: calc(100vh - 120px); /* Prevent going into footer */
    overflow-y: auto; /* Allow scrolling within TOC if content is long */
    background: var(--safeoid-toc-bg-color, #fff);
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    z-index: 999; /* Lower z-index to not interfere with headers */
    font-family: Arial, sans-serif;
    color: var(--safeoid-toc-text-color, #111);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.floating-toc .toc-heading {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    color: var(--safeoid-toc-text-color, #111);
}

.floating-toc .toc-toggle {
    display: none;
}

.floating-toc ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.floating-toc ul li {
    margin: 8px 0;
}

.floating-toc ul li a {
    text-decoration: none;
    color: var(--safeoid-toc-link-color, #0073e6);
    font-size: 14px;
}

.floating-toc ul li a:hover {
    text-decoration: underline;
    color: var(--safeoid-toc-link-hover-color, #005bb5);
}

@media (max-width: 1024px) {
    /* Remove desktop padding on mobile */
    body:has(.floating-toc),
    body.has-floating-toc {
        padding-left: 0;
        margin-top: 60px; /* pushes content below mobile TOC */
    }

    /* Reset header/footer margins on mobile */
    body:has(.floating-toc) header,
    body:has(.floating-toc) .site-header,
    body:has(.floating-toc) footer,
    body:has(.floating-toc) .site-footer,
    body.has-floating-toc header,
    body.has-floating-toc .site-header,
    body.has-floating-toc footer,
    body.has-floating-toc .site-footer {
        margin-left: 0;
        width: 100%;
    }

    .floating-toc {
        top: 0;
        left: 0;
        width: 100%;
        max-height: none;
        overflow-y: visible;
        border-radius: 0;
        border: none;
        padding: 12px 16px;
        z-index: 9999; /* Higher z-index on mobile to stay above content */
    }

    .floating-toc .toc-heading {
        display: none;
    }

    .floating-toc .toc-toggle {
        display: block;
        font-weight: bold;
        font-size: 16px;
        cursor: pointer;
        color: var(--safeoid-toc-text-color, #111);
    }

    .floating-toc .toc-content {
        display: none;
        margin-top: 10px;
    }

    body.toc-open .floating-toc .toc-content {
        display: block;
    }
}

/* Customizable colors via settings */
.floating-toc {
    color: var(--safeoid-toc-text-color, #111);
    background-color: var(--safeoid-toc-bg-color, #ffffff);
}

.floating-toc a {
    color: var(--safeoid-toc-link-color, #0073e6);
}

.floating-toc a:hover {
    color: var(--safeoid-toc-link-hover-color, #005bb5);
}

/* Elementor widget styles */
.elementor-widget-safeoid-toc .floating-toc {
    position: relative;
    top: auto;
    left: auto;
    width: 100%;
    height: auto;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.elementor-widget-safeoid-toc .floating-toc .toc-toggle {
    display: none;
}

/* Ensure TOC doesn't interfere with common theme elements */
.floating-toc {
    /* Make sure it doesn't interfere with headers */
    pointer-events: auto;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* Hide TOC when at footer */
.floating-toc.hide-at-footer {
    opacity: 0;
    visibility: hidden;
}

/* Ensure headers and navigation stay above TOC */
header,
.site-header,
.main-navigation,
nav,
.navbar {
    position: relative;
    z-index: 10000 !important;
}

/* Ensure footers stay below content */
footer,
.site-footer {
    position: relative;
    z-index: 1;
}

/* Additional theme compatibility */
.elementor-location-header,
.elementor-location-footer {
    position: relative;
    z-index: 10000;
}

/* Print styles */
@media print {
    .floating-toc {
        display: none !important;
    }

    body:has(.floating-toc),
    body.has-floating-toc {
        padding-left: 0 !important;
        margin-top: 0 !important;
    }

    body:has(.floating-toc) header,
    body:has(.floating-toc) .site-header,
    body:has(.floating-toc) footer,
    body:has(.floating-toc) .site-footer,
    body.has-floating-toc header,
    body.has-floating-toc .site-header,
    body.has-floating-toc footer,
    body.has-floating-toc .site-footer {
        margin-left: 0 !important;
        width: 100% !important;
    }
}
