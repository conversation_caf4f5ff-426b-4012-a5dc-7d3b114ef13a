<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TOC Test Page</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }
        
        header {
            background: #333;
            color: white;
            padding: 1rem;
            position: relative;
            z-index: 10000;
        }
        
        .content {
            padding: 2rem;
            max-width: 800px;
        }
        
        footer {
            background: #333;
            color: white;
            padding: 2rem;
            margin-top: 3rem;
        }
        
        h1, h2, h3, h4 {
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        
        p {
            margin-bottom: 1rem;
        }
    </style>
</head>
<body class="has-floating-toc">
    <header>
        <h1>Test Website Header</h1>
        <nav>Navigation Menu</nav>
    </header>

    <div class="floating-toc">
        <h3 class="toc-heading">Table of Contents</h3>
        <div class="toc-toggle" onclick="document.body.classList.toggle('toc-open')">☰ Table of Contents</div>
        <div class="toc-content">
            <ul>
                <li><a href="#introduction">Introduction</a></li>
                <li><a href="#getting-started">Getting Started</a></li>
                <li><a href="#benefits">Benefits</a></li>
                <li><a href="#features">Features</a></li>
                <li><a href="#examples">Examples</a></li>
                <li><a href="#conclusion">Conclusion</a></li>
            </ul>
        </div>
    </div>

    <main class="content">
        <h1>Main Page Title</h1>
        <p>This is the main content area. The TOC should be floating on the left side and this content should be pushed to the right.</p>

        <h2 id="introduction">Introduction</h2>
        <p>This is the introduction section. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>

        <h2 id="getting-started">Getting Started</h2>
        <p>This section explains how to get started. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
        <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>

        <h2 id="benefits">Benefits</h2>
        <p>Here are the main benefits. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
        <p>Totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>

        <h2 id="features">Features</h2>
        <p>Main features of the system. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit.</p>
        <p>Sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>

        <h2 id="examples">Examples</h2>
        <p>Real-world examples. Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit.</p>
        <p>Sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.</p>

        <h2 id="conclusion">Conclusion</h2>
        <p>Final thoughts and summary. Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam.</p>
        <p>Nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur.</p>
        
        <p>More content to test scrolling...</p>
        <p>More content to test scrolling...</p>
        <p>More content to test scrolling...</p>
        <p>More content to test scrolling...</p>
        <p>More content to test scrolling...</p>
    </main>

    <footer>
        <h3>Footer Section</h3>
        <p>This is the footer. The TOC should hide when this section becomes visible.</p>
        <p>Copyright 2024 - Test Website</p>
    </footer>

    <script src="js/toc.js"></script>
</body>
</html>
