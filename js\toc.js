/**
 * Safeoid TOC JavaScript
 * Vanilla JS for TOC functionality - no jQuery dependency
 */

(function() {
    'use strict';
    
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTOC);
    } else {
        initTOC();
    }
    
    function initTOC() {
        const tocElement = document.querySelector('.safeoid-toc');
        if (!tocElement) return;
        
        // Initialize mobile toggle functionality
        initMobileToggle();
        
        // Initialize desktop body margin
        initDesktopLayout();
        
        // Initialize smooth scrolling
        initSmoothScrolling();
        
        // Initialize scroll spy (optional future feature)
        // initScrollSpy();
    }
    
    /**
     * Initialize mobile toggle functionality
     */
    function initMobileToggle() {
        const mobileToggle = document.querySelector('.safeoid-toc.mobile .toc-toggle');
        if (!mobileToggle) return;
        
        mobileToggle.addEventListener('click', function() {
            const tocContainer = this.closest('.safeoid-toc');
            tocContainer.classList.toggle('active');
        });
        
        // Close mobile TOC when clicking on a link
        const mobileLinks = document.querySelectorAll('.safeoid-toc.mobile .toc-content a');
        mobileLinks.forEach(function(link) {
            link.addEventListener('click', function() {
                const tocContainer = document.querySelector('.safeoid-toc.mobile');
                if (tocContainer) {
                    tocContainer.classList.remove('active');
                }
            });
        });
    }
    
    /**
     * Initialize desktop layout with body margin
     */
    function initDesktopLayout() {
        const desktopToc = document.querySelector('.safeoid-toc.desktop');
        if (!desktopToc) return;
        
        // Add class to body for desktop TOC
        document.body.classList.add('safeoid-toc-active');
        
        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth < 768) {
                document.body.classList.remove('safeoid-toc-active');
            } else if (desktopToc) {
                document.body.classList.add('safeoid-toc-active');
            }
        });
    }
    
    /**
     * Initialize smooth scrolling for TOC links
     */
    function initSmoothScrolling() {
        const tocLinks = document.querySelectorAll('.safeoid-toc a[href^="#"]');
        
        tocLinks.forEach(function(link) {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    const offsetTop = getElementOffsetTop(targetElement);
                    const scrollOffset = getScrollOffset();
                    
                    window.scrollTo({
                        top: offsetTop - scrollOffset,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }
    
    /**
     * Get element's offset from top of page
     */
    function getElementOffsetTop(element) {
        let offsetTop = 0;
        while (element) {
            offsetTop += element.offsetTop;
            element = element.offsetParent;
        }
        return offsetTop;
    }
    
    /**
     * Get scroll offset for fixed headers
     */
    function getScrollOffset() {
        let offset = 20; // Default offset
        
        // WordPress admin bar
        const adminBar = document.getElementById('wpadminbar');
        if (adminBar) {
            offset += adminBar.offsetHeight;
        }
        
        // Mobile TOC
        const mobileToc = document.querySelector('.safeoid-toc.mobile');
        if (mobileToc && window.innerWidth <= 767) {
            offset += mobileToc.offsetHeight;
        }
        
        // Check for common theme headers
        const stickyHeaders = document.querySelectorAll('.sticky-header, .fixed-header, header.fixed');
        stickyHeaders.forEach(function(header) {
            if (window.getComputedStyle(header).position === 'fixed') {
                offset += header.offsetHeight;
            }
        });
        
        return offset;
    }
    
    /**
     * Future feature: Scroll spy to highlight current section
     * Uncomment and customize as needed
     */
    /*
    function initScrollSpy() {
        const tocLinks = document.querySelectorAll('.safeoid-toc a[href^="#"]');
        const headings = [];
        
        tocLinks.forEach(function(link) {
            const targetId = link.getAttribute('href').substring(1);
            const heading = document.getElementById(targetId);
            if (heading) {
                headings.push({
                    element: heading,
                    link: link,
                    top: getElementOffsetTop(heading)
                });
            }
        });
        
        if (headings.length === 0) return;
        
        function updateActiveLink() {
            const scrollTop = window.pageYOffset;
            const scrollOffset = getScrollOffset();
            
            let activeHeading = headings[0];
            
            for (let i = 0; i < headings.length; i++) {
                if (scrollTop >= headings[i].top - scrollOffset - 50) {
                    activeHeading = headings[i];
                }
            }
            
            // Remove active class from all links
            tocLinks.forEach(function(link) {
                link.classList.remove('active');
            });
            
            // Add active class to current link
            if (activeHeading) {
                activeHeading.link.classList.add('active');
            }
        }
        
        // Throttle scroll events
        let ticking = false;
        function onScroll() {
            if (!ticking) {
                requestAnimationFrame(function() {
                    updateActiveLink();
                    ticking = false;
                });
                ticking = true;
            }
        }
        
        window.addEventListener('scroll', onScroll);
        updateActiveLink(); // Initial call
    }
    */
    
})();
