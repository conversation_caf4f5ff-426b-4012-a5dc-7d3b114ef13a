/**
 * Safeoid TOC JavaScript
 * Simple vanilla JS for TOC functionality
 */

(function() {
    'use strict';

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTOC);
    } else {
        initTOC();
    }

    function initTOC() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        // Add body class for browsers that don't support :has()
        document.body.classList.add('has-floating-toc');

        // Adjust TOC position based on header height
        adjustTOCPosition();

        // Initialize smooth scrolling
        initSmoothScrolling();

        // Readjust on window resize
        window.addEventListener('resize', adjustTOCPosition);
    }

    /**
     * Adjust TOC position based on header height
     */
    function adjustTOCPosition() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement || window.innerWidth <= 1024) return;

        let headerHeight = 20; // Default offset

        // Check for WordPress admin bar
        const adminBar = document.getElementById('wpadminbar');
        if (adminBar) {
            headerHeight += adminBar.offsetHeight;
        }

        // Check for common header elements
        const headers = document.querySelectorAll('header, .site-header, .main-header, .header, .navbar');
        headers.forEach(function(header) {
            const headerRect = header.getBoundingClientRect();
            const headerBottom = headerRect.bottom;
            if (headerBottom > headerHeight) {
                headerHeight = headerBottom;
            }
        });

        // Add some padding
        headerHeight += 20;

        // Apply the calculated top position
        tocElement.style.top = headerHeight + 'px';
        tocElement.style.maxHeight = 'calc(100vh - ' + (headerHeight + 40) + 'px)';
    }

    /**
     * Initialize smooth scrolling for TOC links
     */
    function initSmoothScrolling() {
        const tocLinks = document.querySelectorAll('.floating-toc a[href^="#"]');

        tocLinks.forEach(function(link) {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    const offsetTop = getElementOffsetTop(targetElement);
                    const scrollOffset = getScrollOffset();

                    window.scrollTo({
                        top: offsetTop - scrollOffset,
                        behavior: 'smooth'
                    });

                    // Close mobile TOC after clicking
                    if (window.innerWidth <= 1024) {
                        document.body.classList.remove('toc-open');
                    }
                }
            });
        });
    }

    /**
     * Get element's offset from top of page
     */
    function getElementOffsetTop(element) {
        let offsetTop = 0;
        while (element) {
            offsetTop += element.offsetTop;
            element = element.offsetParent;
        }
        return offsetTop;
    }

    /**
     * Get scroll offset for fixed headers
     */
    function getScrollOffset() {
        let offset = 20; // Default offset

        // WordPress admin bar
        const adminBar = document.getElementById('wpadminbar');
        if (adminBar) {
            offset += adminBar.offsetHeight;
        }

        // Mobile TOC
        if (window.innerWidth <= 1024) {
            offset += 60; // Mobile TOC height
        } else {
            // On desktop, check for fixed/sticky headers
            const headers = document.querySelectorAll('header, .site-header, .main-header, .header, .navbar');
            headers.forEach(function(header) {
                const style = window.getComputedStyle(header);
                if (style.position === 'fixed' || style.position === 'sticky') {
                    offset += header.offsetHeight;
                }
            });
        }

        return offset;
    }
    
    /**
     * Get element's offset from top of page
     */
    function getElementOffsetTop(element) {
        let offsetTop = 0;
        while (element) {
            offsetTop += element.offsetTop;
            element = element.offsetParent;
        }
        return offsetTop;
    }
    
    /**
     * Get scroll offset for fixed headers
     */
    function getScrollOffset() {
        let offset = 20; // Default offset

        // WordPress admin bar
        const adminBar = document.getElementById('wpadminbar');
        if (adminBar) {
            offset += adminBar.offsetHeight;
        }

        // Mobile TOC
        const mobileToc = document.querySelector('.floating-toc');
        if (mobileToc && window.innerWidth <= 1024) {
            offset += 60; // Mobile TOC height
        }

        // Check for common theme headers
        const stickyHeaders = document.querySelectorAll('.sticky-header, .fixed-header, header.fixed');
        stickyHeaders.forEach(function(header) {
            if (window.getComputedStyle(header).position === 'fixed') {
                offset += header.offsetHeight;
            }
        });

        return offset;
    }
    
    /**
     * Future feature: Scroll spy to highlight current section
     * Uncomment and customize as needed
     */
    /*
    function initScrollSpy() {
        const tocLinks = document.querySelectorAll('.safeoid-toc a[href^="#"]');
        const headings = [];
        
        tocLinks.forEach(function(link) {
            const targetId = link.getAttribute('href').substring(1);
            const heading = document.getElementById(targetId);
            if (heading) {
                headings.push({
                    element: heading,
                    link: link,
                    top: getElementOffsetTop(heading)
                });
            }
        });
        
        if (headings.length === 0) return;
        
        function updateActiveLink() {
            const scrollTop = window.pageYOffset;
            const scrollOffset = getScrollOffset();
            
            let activeHeading = headings[0];
            
            for (let i = 0; i < headings.length; i++) {
                if (scrollTop >= headings[i].top - scrollOffset - 50) {
                    activeHeading = headings[i];
                }
            }
            
            // Remove active class from all links
            tocLinks.forEach(function(link) {
                link.classList.remove('active');
            });
            
            // Add active class to current link
            if (activeHeading) {
                activeHeading.link.classList.add('active');
            }
        }
        
        // Throttle scroll events
        let ticking = false;
        function onScroll() {
            if (!ticking) {
                requestAnimationFrame(function() {
                    updateActiveLink();
                    ticking = false;
                });
                ticking = true;
            }
        }
        
        window.addEventListener('scroll', onScroll);
        updateActiveLink(); // Initial call
    }
    */
    
})();
